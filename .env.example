# Application Configuration
NODE_ENV=development
PORT=3000
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# MongoDB Configuration
# Retail POS Database
RETAIL_POS_MONGODB_URI=mongodb://localhost:27017/retail_pos
RETAIL_POS_MONGODB_MAX_POOL_SIZE=10
RETAIL_POS_MONGODB_MIN_POOL_SIZE=5
RETAIL_POS_MONGODB_MAX_IDLE_TIME_MS=30000
RETAIL_POS_MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000





# API Configuration - Retail POS
RETAIL_POS_API_BASE_URL=http://localhost:8001/api
RETAIL_POS_API_TIMEOUT=30000
RETAIL_POS_API_RETRIES=3
RETAIL_POS_API_RETRY_DELAY=1000
RETAIL_POS_API_AUTH_TYPE=bearer
RETAIL_POS_API_AUTH_TOKEN=your-retail-pos-api-token



# Circuit Breaker Configuration
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60000

# Rate Limiting Configuration
# Default throttling
THROTTLE_DEFAULT_TTL=60000
THROTTLE_DEFAULT_LIMIT=100

# Retail POS throttling
THROTTLE_RETAIL_POS_TTL=60000
THROTTLE_RETAIL_POS_LIMIT=200



# Redis Configuration (optional, for advanced throttling)
THROTTLE_REDIS_ENABLED=false
THROTTLE_REDIS_HOST=localhost
THROTTLE_REDIS_PORT=6379
THROTTLE_REDIS_PASSWORD=
THROTTLE_REDIS_DB=0

# Pagination Configuration
PAGINATION_DEFAULT_LIMIT=20
PAGINATION_MAX_LIMIT=100

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json

# Security Configuration
JWT_SECRET=your-jwt-secret-key-here
API_KEY_HEADER=X-API-Key
API_KEYS=key1,key2,key3

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_MONGODB_ENABLED=true

# Monitoring Configuration (optional)
METRICS_ENABLED=false
METRICS_PORT=9090
TRACING_ENABLED=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces