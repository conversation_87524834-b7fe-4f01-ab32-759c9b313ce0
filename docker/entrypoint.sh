#!/bin/bash

# Docker entrypoint script for NestJS application
# Handles common development issues like locked dist directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}[ENTRYPOINT]${NC} Starting NestJS application..."

# Function to safely clean dist directory
clean_dist() {
    echo -e "${YELLOW}[ENTRYPOINT]${NC} Handling dist directory..."

    # Create dist directory if it doesn't exist
    mkdir -p /app/dist

    # Try to clean contents safely
    if [ -d "/app/dist" ] && [ "$(ls -A /app/dist 2>/dev/null)" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Cleaning existing dist contents..."
        find /app/dist -mindepth 1 -delete 2>/dev/null || {
            echo -e "${YELLOW}[ENTRYPOINT]${NC} Standard cleanup failed, trying alternative..."
            rm -rf /app/dist/* 2>/dev/null || true
            rm -rf /app/dist/.* 2>/dev/null || true
        }
    fi

    echo -e "${GREEN}[ENTRYPOINT]${NC} Dist directory ready"
}

# Function to wait for dependencies
wait_for_dependencies() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} Checking dependencies..."

    # Wait for MongoDB (with timeout)
    if command -v nc >/dev/null 2>&1; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Waiting for MongoDB..."
        timeout=30
        while [ $timeout -gt 0 ] && ! nc -z mongodb 27017 2>/dev/null; do
            sleep 1
            timeout=$((timeout - 1))
        done

        if [ $timeout -gt 0 ]; then
            echo -e "${GREEN}[ENTRYPOINT]${NC} MongoDB is ready"
        else
            echo -e "${YELLOW}[ENTRYPOINT]${NC} MongoDB timeout - continuing anyway"
        fi

        # Wait for Kafka (with timeout)
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Waiting for Kafka..."
        timeout=30
        while [ $timeout -gt 0 ] && ! nc -z kafka 9092 2>/dev/null; do
            sleep 1
            timeout=$((timeout - 1))
        done

        if [ $timeout -gt 0 ]; then
            echo -e "${GREEN}[ENTRYPOINT]${NC} Kafka is ready"
        else
            echo -e "${YELLOW}[ENTRYPOINT]${NC} Kafka timeout - continuing anyway"
        fi
    else
        echo -e "${YELLOW}[ENTRYPOINT]${NC} netcat not available, skipping dependency checks"
    fi
}

# Handle different environments
if [ "$NODE_ENV" = "development" ]; then
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running in development mode"
    
    # Clean dist directory for development
    clean_dist
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Install dependencies if node_modules is empty (first run)
    if [ ! -d "/app/node_modules" ] || [ -z "$(ls -A /app/node_modules)" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Installing dependencies..."
        npm install
    fi
    
    # Start development server
    echo -e "${GREEN}[ENTRYPOINT]${NC} Starting development server..."
    exec npm run start:dev
    
elif [ "$NODE_ENV" = "production" ]; then
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running in production mode"
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Start production server
    echo -e "${GREEN}[ENTRYPOINT]${NC} Starting production server..."
    exec node dist/main.js
    
else
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running with custom command"
    # Execute the provided command
    exec "$@"
fi
