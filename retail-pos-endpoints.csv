Method,Endpoint,Description
POST,/retail-pos/brand,Create or update a brand
GET,/retail-pos/:accountId/brands,Get brands for an account
POST,/retail-pos/consignment/inventory-count,Create or update a consignment inventory count
GET,/retail-pos/:accountId/consignment-inventory-counts,Get consignment inventory counts for an account
POST,/retail-pos/consignment/order,Create or update a consignment order
GET,/retail-pos/:accountId/consignment-orders,Get consignment orders for an account
POST,/retail-pos/consignment/return,Create or update a consignment return
GET,/retail-pos/:accountId/consignment-returns,Get consignment returns for an account
POST,/retail-pos/consignment/transfer,Create or update a consignment transfer
GET,/retail-pos/:accountId/consignment-transfers,Get consignment transfers for an account
POST,/retail-pos/customer,Create or update a customer
GET,/retail-pos/:accountId/customers,Get customers for an account
POST,/retail-pos/inventory,Create or update an inventory record
GET,/retail-pos/:accountId/inventory,Get inventory for an account
POST,/retail-pos/location,Create or update a location
GET,/retail-pos/:accountId/locations,Get locations for an account
POST,/retail-pos/location-cash-register,Create or update a cash register in a location
GET,/retail-pos/:accountId/location-cash-registers,Get location cash registers for an account
POST,/retail-pos/payment-type,Create or update a payment type
GET,/retail-pos/:accountId/payment-types,Get payment types for an account
POST,/retail-pos/product,Create or update a product
GET,/retail-pos/:accountId/products,Get products for an account
POST,/retail-pos/product-type,Create or update a product type
GET,/retail-pos/:accountId/product-types,Get product types for an account
POST,/retail-pos/sale,Create or update a sale
GET,/retail-pos/:accountId/sales,Get sales for an account
POST,/retail-pos/supplier,Create or update a supplier
GET,/retail-pos/:accountId/suppliers,Get suppliers for an account
POST,/retail-pos/sync/initiate,Initiate a synchronization
GET,/retail-pos/:accountId/tags,Get tags for an account
POST,/retail-pos/tax,Create or update a tax
GET,/retail-pos/:accountId/taxes,Get taxes for an account
GET,/retail-pos/:accountId/transactions,Get transactions for an account
GET,/retail-pos/:accountId/entities/:entityType,Get entities by type for an account