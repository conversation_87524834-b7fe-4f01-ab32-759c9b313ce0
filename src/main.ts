import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
    credentials: true,
  });

  // Global prefix
  app.setGlobalPrefix('api', {
    exclude: ['/health', '/'],
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Public API Connector')
    .setDescription(`
      A NestJS microservice that acts as a proxy layer between external clients and internal APIs.

      This service provides:
      - HTTP proxy endpoints for retail POS operations
      - Cursor-based pagination for MongoDB data retrieval
      - Circuit breaker patterns for resilient API forwarding
      - Standardized error responses across all endpoints

      ## Authentication
      Most endpoints require authentication via Bearer token or API key.

      ## Rate Limiting
      All endpoints are rate-limited to prevent abuse.

      ## Pagination
      GET endpoints support cursor-based pagination with the following parameters:
      - \`limit\`: Number of items per page (1-100, default: 20)
      - \`cursor\`: Base64-encoded cursor for pagination
      - \`sortField\`: Field to sort by (default: _id)
      - \`sortOrder\`: Sort order (asc/desc, default: desc)
    `)
    .setVersion('1.0.0')
    .addBearerAuth()
    .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' }, 'api-key')
    .addTag('Retail POS', 'Point of sale system integration endpoints')
    .addTag('Health', 'Health check and monitoring endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
    },
  });

  const port = process.env.PORT || 3000;
  await app.listen(port);

  logger.log(`🚀 Application is running on: http://localhost:${port}`);
  logger.log(`📚 Swagger documentation: http://localhost:${port}/api`);
  logger.log(`🏥 Health check: http://localhost:${port}/health`);
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
