import { registerAs } from '@nestjs/config';

export interface DatabaseConfig {
  retailPos: {
    uri: string;
    options: {
      maxPoolSize: number;
      minPoolSize: number;
      maxIdleTimeMS: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
      family: number;
    };
  };
  accounting: {
    uri: string;
    options: {
      maxPoolSize: number;
      minPoolSize: number;
      maxIdleTimeMS: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
      family: number;
    };
  };
  loyalty: {
    uri: string;
    options: {
      maxPoolSize: number;
      minPoolSize: number;
      maxIdleTimeMS: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
      family: number;
    };
  };
}

const defaultMongoOptions = {
  maxPoolSize: 10,
  minPoolSize: 5,
  maxIdleTimeMS: 30000,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4,
};

export default registerAs('database', (): DatabaseConfig => ({
  retailPos: {
    uri: process.env.RETAIL_POS_MONGODB_URI || 'mongodb://localhost:27017/retail_pos',
    options: {
      ...defaultMongoOptions,
      maxPoolSize: parseInt(process.env.RETAIL_POS_MONGODB_MAX_POOL_SIZE || '10', 10),
      minPoolSize: parseInt(process.env.RETAIL_POS_MONGODB_MIN_POOL_SIZE || '5', 10),
      maxIdleTimeMS: parseInt(process.env.RETAIL_POS_MONGODB_MAX_IDLE_TIME_MS || '30000', 10),
      serverSelectionTimeoutMS: parseInt(process.env.RETAIL_POS_MONGODB_SERVER_SELECTION_TIMEOUT_MS || '5000', 10),
    },
  },
  accounting: {
    uri: process.env.ACCOUNTING_MONGODB_URI || 'mongodb://localhost:27017/accounting',
    options: {
      ...defaultMongoOptions,
      maxPoolSize: parseInt(process.env.ACCOUNTING_MONGODB_MAX_POOL_SIZE || '10', 10),
      minPoolSize: parseInt(process.env.ACCOUNTING_MONGODB_MIN_POOL_SIZE || '5', 10),
      maxIdleTimeMS: parseInt(process.env.ACCOUNTING_MONGODB_MAX_IDLE_TIME_MS || '30000', 10),
      serverSelectionTimeoutMS: parseInt(process.env.ACCOUNTING_MONGODB_SERVER_SELECTION_TIMEOUT_MS || '5000', 10),
    },
  },
  loyalty: {
    uri: process.env.LOYALTY_MONGODB_URI || 'mongodb://localhost:27017/loyalty',
    options: {
      ...defaultMongoOptions,
      maxPoolSize: parseInt(process.env.LOYALTY_MONGODB_MAX_POOL_SIZE || '10', 10),
      minPoolSize: parseInt(process.env.LOYALTY_MONGODB_MIN_POOL_SIZE || '5', 10),
      maxIdleTimeMS: parseInt(process.env.LOYALTY_MONGODB_MAX_IDLE_TIME_MS || '30000', 10),
      serverSelectionTimeoutMS: parseInt(process.env.LOYALTY_MONGODB_SERVER_SELECTION_TIMEOUT_MS || '5000', 10),
    },
  },
}));
