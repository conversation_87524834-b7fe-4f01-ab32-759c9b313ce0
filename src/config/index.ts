import databaseConfigFn, { DatabaseConfig } from './database.config';
import apiConfigFn, { ApiConfig } from './api.config';
import throttleConfigFn, { ThrottleConfig } from './throttle.config';
import paginationConfigFn, { PaginationConfig } from './pagination.config';

export { databaseConfigFn as databaseConfig, DatabaseConfig };
export { apiConfigFn as apiConfig, ApiConfig };
export { throttleConfigFn as throttleConfig, ThrottleConfig };
export { paginationConfigFn as paginationConfig, PaginationConfig };

// Configuration array for easy import in modules
export const configurations = [
  databaseConfigFn,
  apiConfigFn,
  throttleConfigFn,
  paginationConfigFn,
];
