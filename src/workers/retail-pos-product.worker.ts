import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { BaseWorker, WorkerMessage, ProcessingResult } from './base.worker';
import { RetailPosProduct } from '../retail-pos/schemas/retail-pos-entity.schema';

@Injectable()
export class RetailPosProductWorker extends BaseWorker {
  constructor(
    configService: ConfigService,
    @InjectModel(RetailPosProduct.name, 'retailPosConnection') 
    private readonly productModel: Model<RetailPosProduct>,
  ) {
    super(configService, 'retail-pos-product', ['retail-pos.product.created', 'retail-pos.product.updated', 'retail-pos.product.deleted']);
  }

  protected async processMessage(message: WorkerMessage): Promise<ProcessingResult> {
    try {
      const { topic, value } = message;
      
      if (!value || !value.productId) {
        return this.fail(new Error('Invalid message: missing productId'));
      }

      this.logger.log(`Processing ${topic} for product: ${value.productId}`);

      switch (topic) {
        case 'retail-pos.product.created':
          return await this.handleProductCreated(value);
        case 'retail-pos.product.updated':
          return await this.handleProductUpdated(value);
        case 'retail-pos.product.deleted':
          return await this.handleProductDeleted(value);
        default:
          return this.fail(new Error(`Unknown topic: ${topic}`));
      }
    } catch (error) {
      this.logger.error('Error processing retail-pos product message:', error);
      return this.retry(error);
    }
  }

  private async handleProductCreated(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const product = new this.productModel({
        accountId: data.accountId,
        productId: data.productId,
        name: data.name,
        sku: data.sku,
        barcode: data.barcode,
        price: data.price,
        quantity: data.quantity || 0,
        category: data.category,
        status: data.status || 'active',
        attributes: data.attributes || {},
        syncedAt: new Date(),
      });

      await product.save();
      this.logger.log(`Product created: ${data.productId}`);
      return this.success();
    } catch (error) {
      if (error.code === 11000) {
        // Duplicate key error - product already exists
        this.logger.warn(`Product already exists: ${data.productId}`);
        return this.success(); // Consider this a success to avoid infinite retries
      }
      return this.retry(error);
    }
  }

  private async handleProductUpdated(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const updateData: Partial<RetailPosProduct> = {
        accountId: data.accountId,
        name: data.name,
        sku: data.sku,
        barcode: data.barcode,
        price: data.price,
        quantity: data.quantity,
        category: data.category,
        status: data.status,
        attributes: data.attributes,
        syncedAt: new Date(),
        updatedAt: new Date(),
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const result = await this.productModel.updateOne(
        { accountId: data.accountId, productId: data.productId },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`Product not found for update: ${data.productId}`);
        // Create the product if it doesn't exist
        return await this.handleProductCreated(data);
      }

      this.logger.log(`Product updated: ${data.productId}`);
      return this.success();
    } catch (error) {
      return this.retry(error);
    }
  }

  private async handleProductDeleted(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const result = await this.productModel.updateOne(
        { accountId: data.accountId, productId: data.productId },
        {
          $set: {
            status: 'deleted',
            syncedAt: new Date(),
            updatedAt: new Date(),
          }
        }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`Product not found for deletion: ${data.productId}`);
        return this.success(); // Consider this a success since the end result is the same
      }

      this.logger.log(`Product deleted: ${data.productId}`);
      return this.success();
    } catch (error) {
      return this.retry(error);
    }
  }
}
