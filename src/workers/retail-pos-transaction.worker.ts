import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { BaseWorker, WorkerMessage, ProcessingResult } from './base.worker';
import { RetailPosTransaction } from '../retail-pos/schemas/retail-pos-entity.schema';

@Injectable()
export class RetailPosTransactionWorker extends BaseWorker {
  constructor(
    configService: ConfigService,
    @InjectModel(RetailPosTransaction.name, 'retailPosConnection') 
    private readonly transactionModel: Model<RetailPosTransaction>,
  ) {
    super(configService, 'retail-pos-transaction', ['retail-pos.transaction.created', 'retail-pos.transaction.updated', 'retail-pos.transaction.completed']);
  }

  protected async processMessage(message: WorkerMessage): Promise<ProcessingResult> {
    try {
      const { topic, value } = message;
      
      if (!value || !value.transactionId) {
        return this.fail(new Error('Invalid message: missing transactionId'));
      }

      this.logger.log(`Processing ${topic} for transaction: ${value.transactionId}`);

      switch (topic) {
        case 'retail-pos.transaction.created':
          return await this.handleTransactionCreated(value);
        case 'retail-pos.transaction.updated':
          return await this.handleTransactionUpdated(value);
        case 'retail-pos.transaction.completed':
          return await this.handleTransactionCompleted(value);
        default:
          return this.fail(new Error(`Unknown topic: ${topic}`));
      }
    } catch (error) {
      this.logger.error('Error processing retail-pos transaction message:', error);
      return this.retry(error);
    }
  }

  private async handleTransactionCreated(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const transaction = new this.transactionModel({
        accountId: data.accountId,
        transactionId: data.transactionId,
        customerId: data.customerId,
        total: data.total,
        subtotal: data.subtotal,
        tax: data.tax || 0,
        discount: data.discount || 0,
        status: data.status || 'pending',
        paymentMethod: data.paymentMethod,
        items: data.items || [],
        storeId: data.storeId,
        cashierId: data.cashierId,
        transactionDate: data.transactionDate ? new Date(data.transactionDate) : new Date(),
        syncedAt: new Date(),
      });

      await transaction.save();
      this.logger.log(`Transaction created: ${data.transactionId}`);
      return this.success();
    } catch (error) {
      if (error.code === 11000) {
        this.logger.warn(`Transaction already exists: ${data.transactionId}`);
        return this.success();
      }
      return this.retry(error);
    }
  }

  private async handleTransactionUpdated(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const updateData: Partial<RetailPosTransaction> = {
        accountId: data.accountId,
        customerId: data.customerId,
        total: data.total,
        subtotal: data.subtotal,
        tax: data.tax,
        discount: data.discount,
        status: data.status,
        paymentMethod: data.paymentMethod,
        items: data.items,
        storeId: data.storeId,
        cashierId: data.cashierId,
        syncedAt: new Date(),
        updatedAt: new Date(),
      };

      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const result = await this.transactionModel.updateOne(
        { accountId: data.accountId, transactionId: data.transactionId },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`Transaction not found for update: ${data.transactionId}`);
        return await this.handleTransactionCreated(data);
      }

      this.logger.log(`Transaction updated: ${data.transactionId}`);
      return this.success();
    } catch (error) {
      return this.retry(error);
    }
  }

  private async handleTransactionCompleted(data: any): Promise<ProcessingResult> {
    try {
      if (!data.accountId) {
        return this.fail(new Error('Invalid message: missing accountId'));
      }

      const result = await this.transactionModel.updateOne(
        { accountId: data.accountId, transactionId: data.transactionId },
        {
          $set: {
            status: 'completed',
            syncedAt: new Date(),
            updatedAt: new Date(),
          }
        }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`Transaction not found for completion: ${data.transactionId}`);
        return this.success();
      }

      this.logger.log(`Transaction completed: ${data.transactionId}`);
      return this.success();
    } catch (error) {
      return this.retry(error);
    }
  }
}
