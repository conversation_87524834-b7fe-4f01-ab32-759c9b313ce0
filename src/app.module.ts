import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerModule } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Configuration imports
import databaseConfig from './config/database.config';
import apiConfig from './config/api.config';
import throttleConfig from './config/throttle.config';
import paginationConfig from './config/pagination.config';

// Module imports
import { CommonModule } from './common/common.module';
import { RetailPosModule } from './retail-pos/retail-pos.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        apiConfig,
        throttleConfig,
        paginationConfig,
      ],
      envFilePath: ['.env.local', '.env'],
    }),

    // Throttling
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const throttleConf = configService.get('throttle');
        return {
          throttlers: [
            {
              name: 'default',
              ttl: throttleConf.global.ttl,
              limit: throttleConf.global.limit,
            },
          ],
          storage: throttleConf.redis?.enabled ? undefined : undefined, // Add Redis storage if needed
        };
      },
      inject: [ConfigService],
    }),

    // MongoDB connections for each vertical
    MongooseModule.forRootAsync({
      connectionName: 'retailPosConnection',
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database');
        return {
          uri: dbConfig.retailPos.uri,
          ...dbConfig.retailPos.options,
        };
      },
      inject: [ConfigService],
    }),



    // Application modules
    CommonModule,
    RetailPosModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
  ],
})
export class AppModule {}
