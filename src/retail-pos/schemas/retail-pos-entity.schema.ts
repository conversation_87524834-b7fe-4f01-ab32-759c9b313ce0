import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Base schema for all retail-pos entities
@Schema({
  timestamps: true,
  collection: 'retail_pos_entities',
  versionKey: false,
})
export class RetailPosEntity extends Document {
  declare _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  accountId: number; // Account ID for multi-tenant filtering

  @Prop({ required: true, index: true })
  entityType: string; // product, transaction, customer, etc.

  @Prop({ type: Object, required: true })
  data: Record<string, any>;

  @Prop({ index: true })
  externalId?: string; // ID from the source system

  @Prop({ default: Date.now, index: true })
  createdAt: Date;

  @Prop({ default: Date.now, index: true })
  updatedAt: Date;

  @Prop({ index: true })
  syncedAt?: Date; // When this was last synced from Kafka

  @Prop({ default: 'active', index: true })
  status: 'active' | 'inactive' | 'deleted';

  @Prop({ type: Object })
  metadata?: Record<string, any>; // Additional metadata
}

export const RetailPosEntitySchema = SchemaFactory.createForClass(RetailPosEntity);

// Create indexes for better performance
RetailPosEntitySchema.index({ accountId: 1, entityType: 1, createdAt: -1 });
RetailPosEntitySchema.index({ accountId: 1, entityType: 1, externalId: 1 }, { unique: true, sparse: true });
RetailPosEntitySchema.index({ accountId: 1, entityType: 1, status: 1, updatedAt: -1 });
RetailPosEntitySchema.index({ accountId: 1, 'data.id': 1 }, { sparse: true });
RetailPosEntitySchema.index({ syncedAt: 1 }, { sparse: true });

// Product-specific schema
@Schema({
  timestamps: true,
  collection: 'retail_pos_products',
  versionKey: false,
})
export class RetailPosProduct extends Document {
  declare _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  accountId: number; // Account ID for multi-tenant filtering

  @Prop({ required: true, index: true })
  productId: string;

  @Prop({ required: true, index: true })
  name: string;

  @Prop({ index: true })
  sku?: string;

  @Prop({ index: true })
  barcode?: string;

  @Prop({ required: true })
  price: number;

  @Prop({ default: 0 })
  quantity: number;

  @Prop({ index: true })
  category?: string;

  @Prop({ default: 'active', index: true })
  status: 'active' | 'inactive' | 'discontinued';

  @Prop({ type: Object })
  attributes?: Record<string, any>;

  @Prop({ default: Date.now, index: true })
  createdAt: Date;

  @Prop({ default: Date.now, index: true })
  updatedAt: Date;

  @Prop({ index: true })
  syncedAt?: Date;
}

export const RetailPosProductSchema = SchemaFactory.createForClass(RetailPosProduct);

// Create indexes for products
RetailPosProductSchema.index({ accountId: 1, productId: 1 }, { unique: true });
RetailPosProductSchema.index({ accountId: 1, status: 1, createdAt: -1 });
RetailPosProductSchema.index({ accountId: 1, category: 1 }, { sparse: true });
RetailPosProductSchema.index({ accountId: 1, sku: 1 }, { sparse: true });
RetailPosProductSchema.index({ accountId: 1, barcode: 1 }, { sparse: true });

// Transaction-specific schema
@Schema({
  timestamps: true,
  collection: 'retail_pos_transactions',
  versionKey: false,
})
export class RetailPosTransaction extends Document {
  declare _id: Types.ObjectId;

  @Prop({ required: true, index: true })
  accountId: number; // Account ID for multi-tenant filtering

  @Prop({ required: true, index: true })
  transactionId: string;

  @Prop({ required: true, index: true })
  customerId?: string;

  @Prop({ required: true })
  total: number;

  @Prop({ required: true })
  subtotal: number;

  @Prop({ default: 0 })
  tax: number;

  @Prop({ default: 0 })
  discount: number;

  @Prop({ required: true, index: true })
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';

  @Prop({ required: true, index: true })
  paymentMethod: string;

  @Prop({ type: [Object], required: true })
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    total: number;
  }>;

  @Prop({ index: true })
  storeId?: string;

  @Prop({ index: true })
  cashierId?: string;

  @Prop({ default: Date.now, index: true })
  transactionDate: Date;

  @Prop({ default: Date.now, index: true })
  createdAt: Date;

  @Prop({ default: Date.now, index: true })
  updatedAt: Date;

  @Prop({ index: true })
  syncedAt?: Date;
}

export const RetailPosTransactionSchema = SchemaFactory.createForClass(RetailPosTransaction);

// Create compound indexes for transactions
RetailPosTransactionSchema.index({ accountId: 1, transactionId: 1 }, { unique: true });
RetailPosTransactionSchema.index({ accountId: 1, status: 1, transactionDate: -1 });
RetailPosTransactionSchema.index({ accountId: 1, customerId: 1, transactionDate: -1 });
RetailPosTransactionSchema.index({ accountId: 1, storeId: 1, transactionDate: -1 });
