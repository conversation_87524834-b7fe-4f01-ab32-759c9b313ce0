import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Base response DTO for entities
export class BaseEntityResponseDto {
  @ApiProperty({ description: 'Unique identifier' })
  _id: string;

  @ApiProperty({ description: 'Account ID' })
  accountId: number;

  @ApiProperty({ description: 'External ID from source system' })
  externalId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Deletion timestamp' })
  deletedAt?: string;

  @ApiPropertyOptional({ description: 'Sync timestamp from Kafka' })
  syncedAt?: string;

  @ApiProperty({ description: 'Entity status', enum: ['active', 'inactive', 'deleted'] })
  status: 'active' | 'inactive' | 'deleted';

  @ApiProperty({ description: 'Entity data' })
  data: Record<string, any>;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;
}

// Brand response DTO
export class BrandResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'brand' })
  entityType: 'brand';
}

// Customer response DTO
export class CustomerResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'customer' })
  entityType: 'customer';
}

// Inventory response DTO
export class InventoryResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'inventory' })
  entityType: 'inventory';
}

// Location response DTO
export class LocationResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'location' })
  entityType: 'location';
}

// Location Cash Register response DTO
export class LocationCashRegisterResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'location-cash-register' })
  entityType: 'location-cash-register';
}

// Payment Type response DTO
export class PaymentTypeResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'payment-type' })
  entityType: 'payment-type';
}

// Product response DTO (from dedicated collection)
export class ProductResponseDto {
  @ApiProperty({ description: 'Unique identifier' })
  _id: string;

  @ApiProperty({ description: 'Account ID' })
  accountId: number;

  @ApiProperty({ description: 'Product ID' })
  productId: string;

  @ApiProperty({ description: 'Product name' })
  name: string;

  @ApiPropertyOptional({ description: 'SKU' })
  sku?: string;

  @ApiPropertyOptional({ description: 'Barcode' })
  barcode?: string;

  @ApiProperty({ description: 'Price' })
  price: number;

  @ApiProperty({ description: 'Quantity' })
  quantity: number;

  @ApiPropertyOptional({ description: 'Category' })
  category?: string;

  @ApiProperty({ description: 'Status', enum: ['active', 'inactive', 'discontinued'] })
  status: 'active' | 'inactive' | 'discontinued';

  @ApiPropertyOptional({ description: 'Product attributes' })
  attributes?: Record<string, any>;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Sync timestamp from Kafka' })
  syncedAt?: string;
}

// Product Type response DTO
export class ProductTypeResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'product-type' })
  entityType: 'product-type';
}

// Sale response DTO
export class SaleResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'sale' })
  entityType: 'sale';
}

// Supplier response DTO
export class SupplierResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'supplier' })
  entityType: 'supplier';
}

// Tag response DTO
export class TagResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'tag' })
  entityType: 'tag';
}

// Tax response DTO
export class TaxResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'tax' })
  entityType: 'tax';
}

// Transaction response DTO (from dedicated collection)
export class TransactionResponseDto {
  @ApiProperty({ description: 'Unique identifier' })
  _id: string;

  @ApiProperty({ description: 'Account ID' })
  accountId: number;

  @ApiProperty({ description: 'Transaction ID' })
  transactionId: string;

  @ApiPropertyOptional({ description: 'Customer ID' })
  customerId?: string;

  @ApiProperty({ description: 'Total amount' })
  total: number;

  @ApiProperty({ description: 'Subtotal amount' })
  subtotal: number;

  @ApiProperty({ description: 'Tax amount' })
  tax: number;

  @ApiProperty({ description: 'Discount amount' })
  discount: number;

  @ApiProperty({ description: 'Transaction status', enum: ['pending', 'completed', 'cancelled', 'refunded'] })
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';

  @ApiProperty({ description: 'Payment method' })
  paymentMethod: string;

  @ApiProperty({ description: 'Transaction items' })
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    total: number;
  }>;

  @ApiPropertyOptional({ description: 'Store ID' })
  storeId?: string;

  @ApiPropertyOptional({ description: 'Cashier ID' })
  cashierId?: string;

  @ApiProperty({ description: 'Transaction date' })
  transactionDate: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Sync timestamp from Kafka' })
  syncedAt?: string;
}

// Consignment response DTOs
export class ConsignmentInventoryCountResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'consignment-inventory-count' })
  entityType: 'consignment-inventory-count';
}

export class ConsignmentOrderResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'consignment-order' })
  entityType: 'consignment-order';
}

export class ConsignmentReturnResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'consignment-return' })
  entityType: 'consignment-return';
}

export class ConsignmentTransferResponseDto extends BaseEntityResponseDto {
  @ApiProperty({ description: 'Entity type', example: 'consignment-transfer' })
  entityType: 'consignment-transfer';
}
