import { IsOptional, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsIn } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';

export class PaginationDto {
  @ApiPropertyOptional({
    description: 'Number of items to return',
    minimum: 1,
    maximum: 100,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Cursor for pagination (base64 encoded)',
    example: 'eyJfaWQiOiI2MGY5ZjQ4ZjQ4ZjQ4ZjQ4ZjQ4ZjQ4ZjQifQ==',
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    default: '_id',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = '_id';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({
    description: 'Search query',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by date range start (ISO string)',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value ? new Date(value).toISOString() : undefined)
  dateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by date range end (ISO string)',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value ? new Date(value).toISOString() : undefined)
  dateTo?: string;
}

export class AccountPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Account ID to filter by',
    example: 12345,
  })
  @Type(() => Number)
  @IsNumber()
  accountId: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  nextCursor: string | null;
  prevCursor: string | null;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

export interface CursorData {
  _id: string;
  [key: string]: any;
}

export class PaginationMeta {
  @ApiPropertyOptional()
  totalCount: number;

  @ApiPropertyOptional()
  nextCursor: string | null;

  @ApiPropertyOptional()
  prevCursor: string | null;

  @ApiPropertyOptional()
  hasNextPage: boolean;

  @ApiPropertyOptional()
  hasPrevPage: boolean;

  @ApiPropertyOptional()
  limit: number;
}
