import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { RetailPosEntity, RetailPosProduct, RetailPosTransaction } from './schemas/retail-pos-entity.schema';
import { PaginationDto, PaginatedResponse, CursorData, AccountPaginationDto } from './dto/pagination.dto';
import { StandardErrorResponse } from './dto/retail-pos-request.dto';
import { ApiConfig } from '../config/api.config';

@Injectable()
export class RetailPosService {
  private readonly logger = new Logger(RetailPosService.name);
  private readonly apiConfig: ApiConfig['retailPos'];

  constructor(
    @InjectModel(RetailPosEntity.name, 'retailPosConnection') private retailPosEntityModel: Model<RetailPosEntity>,
    @InjectModel(RetailPosProduct.name, 'retailPosConnection') private retailPosProductModel: Model<RetailPosProduct>,
    @InjectModel(RetailPosTransaction.name, 'retailPosConnection') private retailPosTransactionModel: Model<RetailPosTransaction>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const apiConfig = this.configService.get<ApiConfig>('api');
    if (!apiConfig) {
      throw new Error('API configuration not found');
    }
    this.apiConfig = apiConfig.retailPos;
  }

  async forwardPost(endpoint: string, payload: any): Promise<any> {
    try {
      const url = `${this.apiConfig.baseUrl}${endpoint}`;
      this.logger.log(`Forwarding POST request to: ${url}`);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authentication headers based on config
      if (this.apiConfig.auth.type === 'bearer' && this.apiConfig.auth.token) {
        headers['Authorization'] = `Bearer ${this.apiConfig.auth.token}`;
      } else if (this.apiConfig.auth.type === 'basic' && this.apiConfig.auth.username && this.apiConfig.auth.password) {
        const credentials = Buffer.from(`${this.apiConfig.auth.username}:${this.apiConfig.auth.password}`).toString('base64');
        headers['Authorization'] = `Basic ${credentials}`;
      } else if (this.apiConfig.auth.type === 'apikey' && this.apiConfig.auth.apiKey) {
        headers[this.apiConfig.auth.apiKeyHeader || 'X-API-Key'] = this.apiConfig.auth.apiKey;
      }

      const response = await firstValueFrom(
        this.httpService.post(url, payload, {
          headers,
          timeout: this.apiConfig.timeout,
        })
      );

      this.logger.log(`Successfully forwarded request to ${url}, status: ${response.status}`);
      return response.data;

    } catch (error) {
      this.logger.error(`Error forwarding request to ${endpoint}:`, error.message);
      
      if (error.response) {
        // Transform API error to standardized format
        const standardError: StandardErrorResponse = {
          statusCode: error.response.status,
          message: error.response.data?.message || 'External API error',
          errors: this.transformApiErrors(error.response.data),
          timestamp: new Date().toISOString(),
          path: endpoint,
        };

        throw new HttpException(standardError, error.response.status);
      }

      // Handle network or other errors
      throw new HttpException(
        {
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
          message: 'Service temporarily unavailable',
          errors: [{ field: 'service', message: 'Unable to connect to external service' }],
          timestamp: new Date().toISOString(),
          path: endpoint,
        },
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  async fetchPaginated(
    collection: string,
    paginationDto: PaginationDto
  ): Promise<PaginatedResponse<any>> {
    try {
      const model = this.getModelByCollection(collection);
      const { limit = 20, cursor, sortBy = '_id', sortOrder = 'desc', search, dateFrom, dateTo } = paginationDto;

      // Build query
      const query: any = {};

      // Add search functionality
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { 'data.name': { $regex: search, $options: 'i' } },
          { 'data.description': { $regex: search, $options: 'i' } },
        ];
      }

      // Add date range filter
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Handle cursor-based pagination
      if (cursor) {
        const cursorData: CursorData = JSON.parse(Buffer.from(cursor, 'base64').toString());
        const sortDirection = sortOrder === 'desc' ? '$lt' : '$gt';
        query[sortBy] = { [sortDirection]: cursorData[sortBy] };
      }

      // Execute query
      const sortObj: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const documents = await model
        .find(query)
        .sort(sortObj)
        .limit(limit + 1) // Get one extra to check if there's a next page
        .lean()
        .exec();

      // Check if there's a next page
      const hasNextPage = documents.length > limit;
      if (hasNextPage) {
        documents.pop(); // Remove the extra document
      }

      // Generate cursors
      const nextCursor = hasNextPage && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[documents.length - 1][sortBy] })).toString('base64')
        : null;

      const prevCursor = cursor && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[0][sortBy] })).toString('base64')
        : null;

      // Get total count (optional, can be expensive for large collections)
      const totalCount = await model.countDocuments(query).exec();

      return {
        data: documents,
        totalCount,
        nextCursor,
        prevCursor,
        hasNextPage,
        hasPrevPage: !!cursor,
        limit,
      };

    } catch (error) {
      this.logger.error(`Error fetching paginated data from ${collection}:`, error.message);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Error fetching data',
          errors: [{ field: 'database', message: error.message }],
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async fetchPaginatedByAccount(
    collection: string,
    accountId: number,
    paginationDto: PaginationDto
  ): Promise<PaginatedResponse<any>> {
    try {
      const model = this.getModelByCollection(collection);
      const { limit = 20, cursor, sortBy = '_id', sortOrder = 'desc', search, dateFrom, dateTo } = paginationDto;

      // Build query with accountId filter
      const query: any = { accountId };

      // Add search functionality
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { 'data.name': { $regex: search, $options: 'i' } },
          { 'data.description': { $regex: search, $options: 'i' } },
        ];
      }

      // Add date range filter
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Handle cursor-based pagination
      if (cursor) {
        const cursorData: CursorData = JSON.parse(Buffer.from(cursor, 'base64').toString());
        const sortDirection = sortOrder === 'desc' ? '$lt' : '$gt';
        query[sortBy] = { [sortDirection]: cursorData[sortBy] };
      }

      // Execute query
      const sortObj: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const documents = await model
        .find(query)
        .sort(sortObj)
        .limit(limit + 1) // Get one extra to check if there's a next page
        .lean()
        .exec();

      // Check if there's a next page
      const hasNextPage = documents.length > limit;
      if (hasNextPage) {
        documents.pop(); // Remove the extra document
      }

      // Generate cursors
      const nextCursor = hasNextPage && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[documents.length - 1][sortBy] })).toString('base64')
        : null;

      const prevCursor = cursor && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[0][sortBy] })).toString('base64')
        : null;

      // Get total count (optional, can be expensive for large collections)
      const totalCount = await model.countDocuments(query).exec();

      return {
        data: documents,
        totalCount,
        nextCursor,
        prevCursor,
        hasNextPage,
        hasPrevPage: !!cursor,
        limit,
      };

    } catch (error) {
      this.logger.error(`Error fetching paginated data from ${collection} for account ${accountId}:`, error.message);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Error fetching data',
          errors: [{ field: 'database', message: error.message }],
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async fetchEntitiesByTypeAndAccount(
    entityType: string,
    accountId: number,
    paginationDto: PaginationDto
  ): Promise<PaginatedResponse<any>> {
    try {
      const model = this.retailPosEntityModel;
      const { limit = 20, cursor, sortBy = '_id', sortOrder = 'desc', search, dateFrom, dateTo } = paginationDto;

      // Build query with accountId and entityType filters
      const query: any = { accountId, entityType };

      // Add search functionality
      if (search) {
        query.$or = [
          { 'data.name': { $regex: search, $options: 'i' } },
          { 'data.description': { $regex: search, $options: 'i' } },
          { 'data.email': { $regex: search, $options: 'i' } },
          { 'data.sku': { $regex: search, $options: 'i' } },
        ];
      }

      // Add date range filter
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Handle cursor-based pagination
      if (cursor) {
        const cursorData: CursorData = JSON.parse(Buffer.from(cursor, 'base64').toString());
        const sortDirection = sortOrder === 'desc' ? '$lt' : '$gt';
        query[sortBy] = { [sortDirection]: cursorData[sortBy] };
      }

      // Execute query
      const sortObj: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const documents = await model
        .find(query)
        .sort(sortObj)
        .limit(limit + 1) // Get one extra to check if there's a next page
        .lean()
        .exec();

      // Check if there's a next page
      const hasNextPage = documents.length > limit;
      if (hasNextPage) {
        documents.pop(); // Remove the extra document
      }

      // Generate cursors
      const nextCursor = hasNextPage && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[documents.length - 1][sortBy] })).toString('base64')
        : null;

      const prevCursor = cursor && documents.length > 0
        ? Buffer.from(JSON.stringify({ [sortBy]: documents[0][sortBy] })).toString('base64')
        : null;

      // Get total count
      const totalCount = await model.countDocuments(query).exec();

      return {
        data: documents,
        totalCount,
        nextCursor,
        prevCursor,
        hasNextPage,
        hasPrevPage: !!cursor,
        limit,
      };

    } catch (error) {
      this.logger.error(`Error fetching entities of type ${entityType} for account ${accountId}:`, error.message);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Error fetching entities',
          errors: [{ field: 'database', message: error.message }],
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private getModelByCollection(collection: string): Model<any> {
    switch (collection) {
      case 'retail_pos_products':
        return this.retailPosProductModel;
      case 'retail_pos_transactions':
        return this.retailPosTransactionModel;
      case 'retail_pos_entities':
      default:
        return this.retailPosEntityModel;
    }
  }

  private transformApiErrors(errorData: any): Array<{ field: string; message: string }> {
    if (!errorData) {
      return [{ field: 'unknown', message: 'Unknown error occurred' }];
    }

    // Handle different error formats
    if (Array.isArray(errorData.errors)) {
      return errorData.errors.map(err => ({
        field: err.field || err.property || 'unknown',
        message: err.message || err.error || 'Validation error',
      }));
    }

    if (errorData.message) {
      return [{ field: 'general', message: errorData.message }];
    }

    return [{ field: 'unknown', message: 'Unknown error occurred' }];
  }
}
