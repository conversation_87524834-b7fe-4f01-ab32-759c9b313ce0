import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Param, 
  Query, 
  HttpCode, 
  HttpStatus, 
  UseGuards,
  Logger,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { RetailPosService } from './retail-pos.service';
import { PaginationDto, PaginatedResponse, AccountPaginationDto } from './dto/pagination.dto';
import { RetailPosRequestDto, StandardErrorResponse } from './dto/retail-pos-request.dto';
import {
  V1BrandDto,
  V1CustomerDto,
  V1InventoryDto,
  V1LocationDto,
  V1LocationCashRegisterDto,
  V1PaymentTypeDto,
  V1ProductDto,
  V1ProductTypeDto,
  V1SupplierDto,
  V1TagDto,
  V1TaxDto,
  V1SaleDto,
  V1SyncInitiateDto,
  V1ConsignmentInventoryCountDto,
  V1ConsignmentOrderDto,
  V1ConsignmentReturnDto,
  V1ConsignmentTransferDto,
} from './dto/openapi-schemas.dto';

@ApiTags('Retail POS')
@Controller('retail-pos')
@ApiBearerAuth()
export class RetailPosController {
  private readonly logger = new Logger(RetailPosController.name);

  constructor(private readonly retailPosService: RetailPosService) {}

  // Brand endpoints
  @Post('brand')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a brand',
    description: 'API to create or update a brand',
    operationId: 'upsertBrand',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertBrand(@Body() brandDto: V1BrandDto): Promise<void> {
    this.logger.log('Upserting brand');
    await this.retailPosService.forwardPost('/brand', brandDto);
  }

  @Get(':accountId/brands')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get brands for an account',
    description: 'Retrieve brands with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter brands',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Brands retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getBrands(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching brands for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('brand', accountId, paginationDto);
  }

  // Consignment endpoints
  @Post('consignment/inventory-count')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a consignment inventory count',
    description: 'API to create or update a consignment inventory count',
    operationId: 'upsertConsignmentInventoryCount',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertConsignmentInventoryCount(@Body() inventoryCountDto: V1ConsignmentInventoryCountDto): Promise<void> {
    this.logger.log('Upserting consignment inventory count');
    await this.retailPosService.forwardPost('/consignment/inventory-count', inventoryCountDto);
  }

  @Post('consignment/order')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a consignment order',
    description: 'API to create or update a consignment order',
    operationId: 'upsertConsignmentOrder',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertConsignmentOrder(@Body() orderDto: V1ConsignmentOrderDto): Promise<void> {
    this.logger.log('Upserting consignment order');
    await this.retailPosService.forwardPost('/consignment/order', orderDto);
  }

  @Post('consignment/return')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a consignment return',
    description: 'API to create or update a consignment return',
    operationId: 'upsertConsignmentReturn',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertConsignmentReturn(@Body() returnDto: V1ConsignmentReturnDto): Promise<void> {
    this.logger.log('Upserting consignment return');
    await this.retailPosService.forwardPost('/consignment/return', returnDto);
  }

  @Post('consignment/transfer')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a consignment transfer',
    description: 'API to create or update a consignment transfer',
    operationId: 'upsertConsignmentTransfer',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertConsignmentTransfer(@Body() transferDto: V1ConsignmentTransferDto): Promise<void> {
    this.logger.log('Upserting consignment transfer');
    await this.retailPosService.forwardPost('/consignment/transfer', transferDto);
  }

  // Customer endpoints
  @Post('customer')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a customer',
    description: 'API to create or update a customer',
    operationId: 'upsertCustomer',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertCustomer(@Body() customerDto: V1CustomerDto): Promise<void> {
    this.logger.log('Upserting customer');
    await this.retailPosService.forwardPost('/customer', customerDto);
  }

  @Get(':accountId/customers')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get customers for an account',
    description: 'Retrieve customers with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter customers',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Customers retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getCustomers(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching customers for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('customer', accountId, paginationDto);
  }

  // Inventory endpoints
  @Post('inventory')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update an inventory record',
    description: 'API to create or update an inventory record',
    operationId: 'upsertInventory',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertInventory(@Body() inventoryDto: V1InventoryDto): Promise<void> {
    this.logger.log('Upserting inventory');
    await this.retailPosService.forwardPost('/inventory', inventoryDto);
  }

  @Get(':accountId/inventory')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get inventory for an account',
    description: 'Retrieve inventory records with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter inventory',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Inventory retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getInventory(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching inventory for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('inventory', accountId, paginationDto);
  }

  // Location endpoints
  @Post('location')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a location',
    description: 'API to create or update a location',
    operationId: 'upsertLocation',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertLocation(@Body() locationDto: V1LocationDto): Promise<void> {
    this.logger.log('Upserting location');
    await this.retailPosService.forwardPost('/location', locationDto);
  }

  @Get(':accountId/locations')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get locations for an account',
    description: 'Retrieve locations with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter locations',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Locations retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getLocations(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching locations for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('location', accountId, paginationDto);
  }

  // Location Cash Register endpoints
  @Post('location-cash-register')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a cash register in a location',
    description: 'API to create or update a cash register in a location',
    operationId: 'upsertLocationCashRegister',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertLocationCashRegister(@Body() cashRegisterDto: V1LocationCashRegisterDto): Promise<void> {
    this.logger.log('Upserting location cash register');
    await this.retailPosService.forwardPost('/location-cash-register', cashRegisterDto);
  }

  @Get(':accountId/location-cash-registers')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get location cash registers for an account',
    description: 'Retrieve location cash registers with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter location cash registers',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Location cash registers retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getLocationCashRegisters(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching location cash registers for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('location-cash-register', accountId, paginationDto);
  }

  // Payment Type endpoints
  @Post('payment-type')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a payment type',
    description: 'API to create or update a payment type',
    operationId: 'upsertPaymentType',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertPaymentType(@Body() paymentTypeDto: V1PaymentTypeDto): Promise<void> {
    this.logger.log('Upserting payment type');
    await this.retailPosService.forwardPost('/payment-type', paymentTypeDto);
  }

  @Get(':accountId/payment-types')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get payment types for an account',
    description: 'Retrieve payment types with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter payment types',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Payment types retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getPaymentTypes(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching payment types for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('payment-type', accountId, paginationDto);
  }

  // Product endpoints
  @Post('product')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a product',
    description: 'API to create or update a product',
    operationId: 'upsertProduct',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertProduct(@Body() productDto: V1ProductDto): Promise<void> {
    this.logger.log('Upserting product');
    await this.retailPosService.forwardPost('/product', productDto);
  }

  @Get(':accountId/products')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get products for an account',
    description: 'Retrieve products with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter products',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getProductsByAccount(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching products for account: ${accountId}`);
    return this.retailPosService.fetchPaginatedByAccount('retail_pos_products', accountId, paginationDto);
  }

  // Product Type endpoints
  @Post('product-type')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a product type',
    description: 'API to create or update a product type',
    operationId: 'upsertProductType',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertProductType(@Body() productTypeDto: V1ProductTypeDto): Promise<void> {
    this.logger.log('Upserting product type');
    await this.retailPosService.forwardPost('/product-type', productTypeDto);
  }

  @Get(':accountId/product-types')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get product types for an account',
    description: 'Retrieve product types with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter product types',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Product types retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getProductTypes(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching product types for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('product-type', accountId, paginationDto);
  }

  // Sale endpoints
  @Post('sale')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a sale',
    description: 'API to create or update a sale',
    operationId: 'upsertSale',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertSale(@Body() saleDto: V1SaleDto): Promise<void> {
    this.logger.log('Upserting sale');
    await this.retailPosService.forwardPost('/sale', saleDto);
  }

  @Get(':accountId/sales')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get sales for an account',
    description: 'Retrieve sales with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter sales',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Sales retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getSales(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching sales for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('sale', accountId, paginationDto);
  }

  // Supplier endpoints
  @Post('supplier')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a supplier',
    description: 'API to create or update a supplier',
    operationId: 'upsertSupplier',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertSupplier(@Body() supplierDto: V1SupplierDto): Promise<void> {
    this.logger.log('Upserting supplier');
    await this.retailPosService.forwardPost('/supplier', supplierDto);
  }

  @Get(':accountId/suppliers')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get suppliers for an account',
    description: 'Retrieve suppliers with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter suppliers',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Suppliers retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getSuppliers(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching suppliers for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('supplier', accountId, paginationDto);
  }

  // Sync endpoints
  @Post('sync/initiate')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Initiate a synchronization',
    description: 'API to initiate a synchronization',
    operationId: 'initiateSync',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async initiateSync(@Body() syncDto: V1SyncInitiateDto): Promise<void> {
    this.logger.log('Initiating sync');
    await this.retailPosService.forwardPost('/sync/initiate', syncDto);
  }

  // Tag endpoints
  @Post('tag')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a tag',
    description: 'API to create or update a tag',
    operationId: 'upsertTag',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertTag(@Body() tagDto: V1TagDto): Promise<void> {
    this.logger.log('Upserting tag');
    await this.retailPosService.forwardPost('/tag', tagDto);
  }

  @Get(':accountId/tags')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get tags for an account',
    description: 'Retrieve tags with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter tags',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Tags retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getTags(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching tags for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('tag', accountId, paginationDto);
  }

  // Tax endpoints
  @Post('tax')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Create or update a tax',
    description: 'API to create or update a tax',
    operationId: 'upsertTax',
  })
  @ApiResponse({
    status: 204,
    description: 'When the operation was successful',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request, failed to parse the request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'The authenticated user has no permission for this operation',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable entity, the request contained invalid values',
    type: StandardErrorResponse,
  })
  async upsertTax(@Body() taxDto: V1TaxDto): Promise<void> {
    this.logger.log('Upserting tax');
    await this.retailPosService.forwardPost('/tax', taxDto);
  }

  @Get(':accountId/taxes')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get taxes for an account',
    description: 'Retrieve taxes with cursor-based pagination filtered by accountId',
  })
  @ApiParam({
    name: 'accountId',
    description: 'Account ID to filter taxes',
    example: 12345,
  })
  @ApiResponse({
    status: 200,
    description: 'Taxes retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: StandardErrorResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden',
    type: StandardErrorResponse,
  })
  async getTaxes(
    @Param('accountId') accountId: number,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching taxes for account: ${accountId}`);
    return this.retailPosService.fetchEntitiesByTypeAndAccount('tax', accountId, paginationDto);
  }

  @Get('products')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get paginated products from MongoDB',
    description: 'Retrieve products with cursor-based pagination and filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        totalCount: { type: 'number' },
        nextCursor: { type: 'string', nullable: true },
        prevCursor: { type: 'string', nullable: true },
        hasNextPage: { type: 'boolean' },
        hasPrevPage: { type: 'boolean' },
        limit: { type: 'number' },
      },
    },
  })
  async getProducts(@Query() paginationDto: PaginationDto): Promise<PaginatedResponse<any>> {
    this.logger.log('Fetching paginated products');
    return this.retailPosService.fetchPaginated('retail_pos_products', paginationDto);
  }

  @Get('transactions')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get paginated transactions from MongoDB',
    description: 'Retrieve transactions with cursor-based pagination and filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Transactions retrieved successfully',
  })
  async getTransactions(@Query() paginationDto: PaginationDto): Promise<PaginatedResponse<any>> {
    this.logger.log('Fetching paginated transactions');
    return this.retailPosService.fetchPaginated('retail_pos_transactions', paginationDto);
  }

  @Get('entities/:entityType')
  @Throttle({ default: { limit: 100, ttl: 60000 } })
  @ApiOperation({
    summary: 'Get paginated entities by type from MongoDB',
    description: 'Retrieve entities of a specific type with cursor-based pagination',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity to retrieve',
    example: 'customer',
  })
  @ApiResponse({
    status: 200,
    description: 'Entities retrieved successfully',
  })
  async getEntitiesByType(
    @Param('entityType') entityType: string,
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(`Fetching paginated entities of type: ${entityType}`);
    // Add entityType filter to the pagination
    const modifiedPagination = { ...paginationDto };
    return this.retailPosService.fetchPaginated('retail_pos_entities', modifiedPagination);
  }
}
