"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });
    const configService = app.get(config_1.ConfigService);
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.enableCors({
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
        credentials: true,
    });
    app.setGlobalPrefix('api', {
        exclude: ['/health', '/'],
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Public API Connector')
        .setDescription(`
      A NestJS microservice that acts as a proxy layer between external clients and internal APIs.

      This service provides:
      - HTTP proxy endpoints for retail POS operations
      - Cursor-based pagination for MongoDB data retrieval
      - Circuit breaker patterns for resilient API forwarding
      - Standardized error responses across all endpoints

      ## Authentication
      Most endpoints require authentication via Bearer token or API key.

      ## Rate Limiting
      All endpoints are rate-limited to prevent abuse.

      ## Pagination
      GET endpoints support cursor-based pagination with the following parameters:
      - \`limit\`: Number of items per page (1-100, default: 20)
      - \`cursor\`: Base64-encoded cursor for pagination
      - \`sortField\`: Field to sort by (default: _id)
      - \`sortOrder\`: Sort order (asc/desc, default: desc)
    `)
        .setVersion('1.0.0')
        .addBearerAuth()
        .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' }, 'api-key')
        .addTag('Retail POS', 'Point of sale system integration endpoints')
        .addTag('Health', 'Health check and monitoring endpoints')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            filter: true,
            showExtensions: true,
            showCommonExtensions: true,
        },
    });
    const port = process.env.PORT || 3000;
    await app.listen(port);
    logger.log(`🚀 Application is running on: http://localhost:${port}`);
    logger.log(`📚 Swagger documentation: http://localhost:${port}/api`);
    logger.log(`🏥 Health check: http://localhost:${port}/health`);
}
bootstrap().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map