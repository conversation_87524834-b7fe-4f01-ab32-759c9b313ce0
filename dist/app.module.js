"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const throttler_1 = require("@nestjs/throttler");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const database_config_1 = require("./config/database.config");
const api_config_1 = require("./config/api.config");
const throttle_config_1 = require("./config/throttle.config");
const pagination_config_1 = require("./config/pagination.config");
const common_module_1 = require("./common/common.module");
const retail_pos_module_1 = require("./retail-pos/retail-pos.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [
                    database_config_1.default,
                    api_config_1.default,
                    throttle_config_1.default,
                    pagination_config_1.default,
                ],
                envFilePath: ['.env.local', '.env'],
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const throttleConf = configService.get('throttle');
                    return {
                        throttlers: [
                            {
                                name: 'default',
                                ttl: throttleConf.global.ttl,
                                limit: throttleConf.global.limit,
                            },
                        ],
                        storage: throttleConf.redis?.enabled ? undefined : undefined,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            mongoose_1.MongooseModule.forRootAsync({
                connectionName: 'retailPosConnection',
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const dbConfig = configService.get('database');
                    return {
                        uri: dbConfig.retailPos.uri,
                        ...dbConfig.retailPos.options,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            common_module_1.CommonModule,
            retail_pos_module_1.RetailPosModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map