import databaseConfigFn, { DatabaseConfig } from './database.config';
import apiConfigFn, { ApiConfig } from './api.config';
import throttleConfigFn, { ThrottleConfig } from './throttle.config';
import paginationConfigFn, { PaginationConfig } from './pagination.config';
export { databaseConfigFn as databaseConfig, DatabaseConfig };
export { apiConfigFn as apiConfig, ApiConfig };
export { throttleConfigFn as throttleConfig, ThrottleConfig };
export { paginationConfigFn as paginationConfig, PaginationConfig };
export declare const configurations: (((() => DatabaseConfig) & import("@nestjs/config").ConfigFactoryKeyHost<DatabaseConfig>) | ((() => ApiConfig) & import("@nestjs/config").ConfigFactoryKeyHost<ApiConfig>) | ((() => ThrottleConfig) & import("@nestjs/config").ConfigFactoryKeyHost<ThrottleConfig>) | ((() => PaginationConfig) & import("@nestjs/config").ConfigFactoryKeyHost<PaginationConfig>))[];
