const topics = {
  "pageCount": 1,
  "topics": [
    {
      "name": "accountingAccount.accountIntegration",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 22384,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1012,
          "offsetMin": 973
        }
      ]
    },
    {
      "name": "accountingDistributor.billPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1403,
          "offsetMin": 1403
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.chartOfAccountPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.classPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 20983,
          "offsetMin": 20983
        }
      ]
    },
    {
      "name": "accountingDistributor.customerPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 10532,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 44976,
          "offsetMin": 44965
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.inventoryAdjustmentPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 112156,
          "offsetMin": 112156
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.invoicePost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 117074,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 116998,
          "offsetMin": 116949
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.journalEntryPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 300,
          "offsetMin": 300
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.multiJournalEntryPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 24,
          "offsetMin": 24
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.paymentTypePost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 70132,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2496,
          "offsetMin": 2379
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.productPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 2051850,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1145173,
          "offsetMin": 1144407
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.purchaseOrderPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 895,
          "offsetMin": 895
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "accountingDistributor.vendorPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 7056,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 5018,
          "offsetMin": 5007
        }
      ]
    },
    {
      "name": "foodAccount.account",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 296,
          "offsetMin": 296
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 307,
          "offsetMin": 307
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 306,
          "offsetMin": 306
        }
      ]
    },
    {
      "name": "foodAccount.accountIntegration",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 886,
          "offsetMin": 886
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 977,
          "offsetMin": 977
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 929,
          "offsetMin": 929
        }
      ]
    },
    {
      "name": "loyaltyAccount.accountIntegration",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 833,
          "offsetMin": 833
        }
      ]
    },
    {
      "name": "loyaltyDistributor.changeEvent",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 438,
          "offsetMin": 438
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 65,
          "offsetMin": 65
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 4103785,
          "offsetMin": 4103785
        }
      ]
    },
    {
      "name": "loyaltyDistributor.customerPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 1,
      "replicas": 3,
      "inSyncReplicas": 3,
      "segmentSize": 0,
      "segmentCount": 3,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 234,
          "offsetMin": 234
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "loyaltyDistributor.customerSearchPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 156,
          "offsetMin": 156
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "loyaltyDistributor.eligibleRewardByOrderPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 1,
      "replicas": 3,
      "inSyncReplicas": 3,
      "segmentSize": 0,
      "segmentCount": 3,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 117,
          "offsetMin": 117
        }
      ]
    },
    {
      "name": "loyaltyDistributor.eligibleRewardPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 1,
      "replicas": 3,
      "inSyncReplicas": 3,
      "segmentSize": 0,
      "segmentCount": 3,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 175,
          "offsetMin": 175
        }
      ]
    },
    {
      "name": "loyaltyDistributor.orderPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 1,
      "replicas": 3,
      "inSyncReplicas": 3,
      "segmentSize": 0,
      "segmentCount": 3,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 6528,
          "offsetMin": 6528
        }
      ]
    },
    {
      "name": "loyaltyDistributor.redeemRewardPost",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 1,
      "replicas": 3,
      "inSyncReplicas": 3,
      "segmentSize": 0,
      "segmentCount": 3,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 52,
          "offsetMin": 52
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "menuDistributor.entityTargetMapping",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 351,
          "offsetMin": 351
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 9474,
          "offsetMin": 9474
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "menuDistributor.menuChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 1054918802,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3606570,
          "offsetMin": 3606570
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 18665103,
          "offsetMin": 18169672
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 5110974,
          "offsetMin": 4813677
        }
      ]
    },
    {
      "name": "orderDistributor.orderChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2629,
          "offsetMin": 2629
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 22550,
          "offsetMin": 22550
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2558,
          "offsetMin": 2558
        }
      ]
    },
    {
      "name": "retailPos.distributor.cashRegisterAddMoney",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "retailPos.distributor.cashRegisterClosing",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 61,
          "offsetMin": 61
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 64,
          "offsetMin": 64
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 115,
          "offsetMin": 115
        }
      ]
    },
    {
      "name": "retailPos.distributor.cashRegisterOpening",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 60,
          "offsetMin": 60
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 52,
          "offsetMin": 52
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 110,
          "offsetMin": 110
        }
      ]
    },
    {
      "name": "retailPos.distributor.categoryChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "retailPos.distributor.consignment.orderChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2658,
          "offsetMin": 2658
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1307,
          "offsetMin": 1307
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1429,
          "offsetMin": 1429
        }
      ]
    },
    {
      "name": "retailPos.distributor.consignment.transferChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3676,
          "offsetMin": 3676
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 289,
          "offsetMin": 289
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 80759,
          "offsetMin": 80759
        }
      ]
    },
    {
      "name": "retailPos.distributor.customerChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 19063,
          "offsetMin": 19063
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 25266,
          "offsetMin": 25266
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 41413,
          "offsetMin": 41413
        }
      ]
    },
    {
      "name": "retailPos.distributor.inventoryChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 164826,
          "offsetMin": 164826
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 15815,
          "offsetMin": 15815
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 36656,
          "offsetMin": 36656
        }
      ]
    },
    {
      "name": "retailPos.distributor.locationCashRegisterChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 73,
          "offsetMin": 73
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 90,
          "offsetMin": 90
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 80,
          "offsetMin": 80
        }
      ]
    },
    {
      "name": "retailPos.distributor.locationChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 25819,
          "offsetMin": 25819
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 331,
          "offsetMin": 331
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1206,
          "offsetMin": 1206
        }
      ]
    },
    {
      "name": "retailPos.distributor.paymentTypeChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 145500,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 653,
          "offsetMin": 653
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3390,
          "offsetMin": 3363
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1277,
          "offsetMin": 1124
        }
      ]
    },
    {
      "name": "retailPos.distributor.productChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 3628514,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1769447,
          "offsetMin": 1769447
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 629143,
          "offsetMin": 629014
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 148433,
          "offsetMin": 147649
        }
      ]
    },
    {
      "name": "retailPos.distributor.productTypeChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 1904,
          "offsetMin": 1904
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3544,
          "offsetMin": 3544
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3035,
          "offsetMin": 3035
        }
      ]
    },
    {
      "name": "retailPos.distributor.purchaseOrderChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "retailPos.distributor.saleChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 208414,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 177328,
          "offsetMin": 177328
        },
        {
          "partition": 1,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 122711,
          "offsetMin": 122685
        },
        {
          "partition": 2,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 231049,
          "offsetMin": 231014
        }
      ]
    },
    {
      "name": "retailPos.distributor.supplierChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2095,
          "offsetMin": 2095
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 2600,
          "offsetMin": 2600
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 5051,
          "offsetMin": 5051
        }
      ]
    },
    {
      "name": "retailPos.distributor.tagChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 1,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    },
    {
      "name": "retailPos.distributor.taxChange",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3455,
          "offsetMin": 3455
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 3062,
          "offsetMin": 3062
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 4649,
          "offsetMin": 4649
        }
      ]
    },
    {
      "name": "retailposAccount.account",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 18070,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 905,
          "offsetMin": 896
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 946,
          "offsetMin": 943
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 975,
          "offsetMin": 965
        }
      ]
    },
    {
      "name": "retailposAccount.accountIntegration",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 32568,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 371,
          "offsetMin": 364
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 418,
          "offsetMin": 414
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 364,
          "offsetMin": 355
        }
      ]
    },
    {
      "name": "standloneZinrelo.locations",
      "internal": false,
      "partitionCount": 3,
      "replicationFactor": 2,
      "replicas": 6,
      "inSyncReplicas": 6,
      "segmentSize": 0,
      "segmentCount": 6,
      "bytesInPerSec": null,
      "bytesOutPerSec": null,
      "underReplicatedPartitions": 0,
      "cleanUpPolicy": "DELETE",
      "partitions": [
        {
          "partition": 0,
          "leader": 2,
          "replicas": [
            {
              "broker": 2,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 1,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 4,
          "offsetMin": 4
        },
        {
          "partition": 1,
          "leader": 1,
          "replicas": [
            {
              "broker": 1,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 0,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        },
        {
          "partition": 2,
          "leader": 0,
          "replicas": [
            {
              "broker": 0,
              "leader": true,
              "inSync": true
            },
            {
              "broker": 2,
              "leader": false,
              "inSync": true
            }
          ],
          "offsetMax": 0,
          "offsetMin": 0
        }
      ]
    }
  ]
}

const refinedTopics = topics.map((t) => {
  
})