Method,Endpoint,Description
GET,/brands,Retrieve list of all brands
GET,/brands/{externalId},Retrieve a single brand by externalId
POST,/brands,Create a new brand
PUT,/brands/{externalId},Update an existing brand
DELETE,/brands/{externalId},Delete a brand

GET,/consignment-inventory-counts,Retrieve list of all consignment inventory counts
GET,/consignment-inventory-counts/{externalId},Retrieve a single consignment inventory count by externalId
POST,/consignment-inventory-counts,Create a new consignment inventory count
PUT,/consignment-inventory-counts/{externalId},Update an existing consignment inventory count
DELETE,/consignment-inventory-counts/{externalId},Delete a consignment inventory count

GET,/consignment-orders,Retrieve list of all consignment orders
GET,/consignment-orders/{externalId},Retrieve a single consignment order by externalId
POST,/consignment-orders,Create a new consignment order
PUT,/consignment-orders/{externalId},Update an existing consignment order
DELETE,/consignment-orders/{externalId},Delete a consignment order

GET,/consignment-returns,Retrieve list of all consignment returns
GET,/consignment-returns/{externalId},Retrieve a single consignment return by externalId
POST,/consignment-returns,Create a new consignment return
PUT,/consignment-returns/{externalId},Update an existing consignment return
DELETE,/consignment-returns/{externalId},Delete a consignment return

GET,/consignment-transfers,Retrieve list of all consignment transfers
GET,/consignment-transfers/{externalId},Retrieve a single consignment transfer by externalId
POST,/consignment-transfers,Create a new consignment transfer
PUT,/consignment-transfers/{externalId},Update an existing consignment transfer
DELETE,/consignment-transfers/{externalId},Delete a consignment transfer

GET,/customers,Retrieve list of all customers
GET,/customers/{externalId},Retrieve a single customer by externalId
POST,/customers,Create a new customer
PUT,/customers/{externalId},Update an existing customer
DELETE,/customers/{externalId},Delete a customer

GET,/inventories,Retrieve list of all inventory records
GET,/inventories/{externalId},Retrieve a single inventory record by externalId
POST,/inventories,Create a new inventory record
PUT,/inventories/{externalId},Update an existing inventory record
DELETE,/inventories/{externalId},Delete an inventory record

GET,/cash-registers,Retrieve list of all location cash registers
GET,/cash-registers/{externalId},Retrieve a single cash register by externalId
POST,/cash-registers,Create a new cash register
PUT,/cash-registers/{externalId},Update an existing cash register
DELETE,/cash-registers/{externalId},Delete a cash register

GET,/locations,Retrieve list of all locations
GET,/locations/{externalId},Retrieve a single location by externalId
POST,/locations,Create a new location
PUT,/locations/{externalId},Update an existing location
DELETE,/locations/{externalId},Delete a location

GET,/payment-types,Retrieve list of all payment types
GET,/payment-types/{externalId},Retrieve a single payment type by externalId
POST,/payment-types,Create a new payment type
PUT,/payment-types/{externalId},Update an existing payment type
DELETE,/payment-types/{externalId},Delete a payment type

GET,/product-types,Retrieve list of all product types
GET,/product-types/{externalId},Retrieve a single product type by externalId
POST,/product-types,Create a new product type
PUT,/product-types/{externalId},Update an existing product type
DELETE,/product-types/{externalId},Delete a product type

GET,/products,Retrieve list of all products
GET,/products/{externalId},Retrieve a single product by externalId
POST,/products,Create a new product
PUT,/products/{externalId},Update an existing product
DELETE,/products/{externalId},Delete a product

GET,/sales,Retrieve list of all sales
GET,/sales/{externalId},Retrieve a single sale by externalId
POST,/sales,Create a new sale
PUT,/sales/{externalId},Update an existing sale
DELETE,/sales/{externalId},Delete a sale

GET,/suppliers,Retrieve list of all suppliers
GET,/suppliers/{externalId},Retrieve a single supplier by externalId
POST,/suppliers,Create a new supplier
PUT,/suppliers/{externalId},Update an existing supplier
DELETE,/suppliers/{externalId},Delete a supplier

GET,/sync-initiations,Retrieve list of all sync initiations
GET,/sync-initiations/{externalId},Retrieve a single sync initiation by externalId
POST,/sync-initiations,Create a new sync initiation
PUT,/sync-initiations/{externalId},Update an existing sync initiation
DELETE,/sync-initiations/{externalId},Delete a sync initiation

GET,/tags,Retrieve list of all tags
GET,/tags/{externalId},Retrieve a single tag by externalId
POST,/tags,Create a new tag
PUT,/tags/{externalId},Update an existing tag
DELETE,/tags/{externalId},Delete a tag

GET,/taxes,Retrieve list of all taxes
GET,/taxes/{externalId},Retrieve a single tax by externalId
POST,/taxes,Create a new tax
PUT,/taxes/{externalId},Update an existing tax
DELETE,/taxes/{externalId},Delete a tax