{"openapi": "3.0.0", "info": {"title": "Public API Connector", "description": "\n A NestJS microservice that acts as a proxy layer between external clients and internal APIs.\n\n This service provides:\n - HTTP proxy endpoints for retail POS operations\n - Cursor-based pagination for MongoDB data retrieval\n - Circuit breaker patterns for resilient API forwarding\n - Standardized error responses across all endpoints\n\n ## Authentication\n Most endpoints require authentication via Bearer token or API key.\n\n ## Rate Limiting\n All endpoints are rate-limited to prevent abuse.\n\n ## Pagination\n GET endpoints support cursor-based pagination with the following parameters:\n - `limit`: Number of items per page (1-100, default: 20)\n - `cursor`: Base64-encoded cursor for pagination\n - `sortField`: Field to sort by (default: _id)\n - `sortOrder`: Sort order (asc/desc, default: desc)\n ", "version": "1.0.0", "contact": {}}, "tags": [{"name": "Retail POS", "description": "Point of sale system integration endpoints"}, {"name": "Health", "description": "Health check and monitoring endpoints"}], "servers": [], "paths": {"/health": {"get": {"description": "Returns the health status of the service and its dependencies", "operationId": "AppController_getHealth", "parameters": [], "responses": {"200": {"description": "Health check successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "string"}, "uptime": {"type": "number"}, "dependencies": {"type": "object", "properties": {"mongodb": {"type": "string"}}}}}}}}}, "summary": "Health check endpoint", "tags": ["Health"]}}}, "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}, "api-key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}, "schemas": {"V1BrandDto": {"type": "object", "properties": {"accountId": {"type": "number", "description": "Id of the account the entity belongs to"}, "externalId": {"type": "string", "description": "Id of the entity in the external system"}, "createdAt": {"type": "string", "description": "Time of the creation of the entity if available"}, "updatedAt": {"type": "string", "description": "Time the entity was last updated if available"}, "deletedAt": {"type": "string", "description": "Time of the deletion if it was deleted"}, "isInitial": {"type": "boolean", "description": "True if the request is because of an initial sync", "default": false}, "name": {"type": "string", "description": "Name of the brand"}}, "required": ["accountId", "externalId", "name"]}, "StandardErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "HTTP status code", "example": 400}, "message": {"type": "string", "description": "Error message", "example": "Validation failed"}, "errors": {"description": "Detailed error information", "example": [{"field": "productId", "message": "Product ID is required"}], "type": "array", "items": {"type": "object"}}, "timestamp": {"type": "string", "description": "Timestamp of the error", "example": "2023-12-07T10:30:00.000Z"}, "path": {"type": "string", "description": "Request path that caused the error", "example": "/api/retail-pos/products"}}, "required": ["statusCode", "message", "errors"]}}}}